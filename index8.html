<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Favourite Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .container {
            padding: 32px 32px 0 32px;
            height: 100%;
            position: relative;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 48px;
        }

        .logo-text {
            height: 48px;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background-color: #E7E7E7;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .bell-icon {
            width: 21px;
            height: 21px;
        }

        .notification-badge {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 8px;
            height: 8px;
            background-color: #ff5757;
            border-radius: 4px;
        }

        /* Title */
        .page-title {
            font-size: 24px;
            font-weight: 700;
            color: #1f1f1f;
            margin-bottom: 28px;
        }

        /* Podcast List */
        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 32px;
            padding-bottom: calc(88px + 16px);
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            height: 96px;
        }

        .podcast-cover {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f1f1f;
            line-height: 1.2;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 1.2;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            line-height: 1.2;
        }

        .play-btn {
            width: 56px;
            height: 56px;
            background-color: #F4ECFF;
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(112, 0, 255, 0.15);
        }

        .play-icon {
            width: 0;
            height: 0;
            border-left: 12px solid #4E00C8;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
            margin-left: 3px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 88px;
            backdrop-filter: blur(24px);
            background: linear-gradient(135deg, rgba(149, 117, 205, 0.25) 0%, rgba(109, 93, 211, 0.25) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 84px;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
        }

        .nav-icon.active {
            opacity: 1;
        }

        .nav-icon.active::after {
            content: '';
            display: block;
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
            margin: 8px auto 0;
        }

        /* Gradient overlay at bottom */
        .gradient-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 156px;
            background: linear-gradient(to top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                <img src="images/ncast-icon.svg" alt="NCast Icon" class="logo-icon">
                <img src="images/ncast-logo-text.svg" alt="NCast" class="logo-text">
            </div>
            <div class="notification-container">
                <button class="notification-btn">
                    <img src="images/bell-icon.png" alt="Notifications" class="bell-icon">
                </button>
                <div class="notification-badge"></div>
            </div>
        </div>

        <!-- Page Title -->
        <h1 class="page-title">Favourite Podcasts</h1>

        <!-- Podcast List -->
        <div class="podcast-list">
            <div class="podcast-item">
                <img src="images/podcast-cover-1.png" alt="Sunday Summer - Ep3" class="podcast-cover">
                <div class="meta">
                    <div class="podcast-title">Sunday Summer - Ep3</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-2.png" alt="Musical Soul - Vol. 1" class="podcast-cover">
                <div class="meta">
                    <div class="podcast-title">Musical Soul - Vol. 1</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">35 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-3.png" alt="Talk Show - Ep4" class="podcast-cover">
                <div class="meta">
                    <div class="podcast-title">Talk Show - Ep4</div>
                    <div class="podcast-category">Business</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-4.png" alt="Musical Soul - Vol. 2" class="podcast-cover">
                <div class="meta">
                    <div class="podcast-title">Musical Soul - Vol. 2</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">30 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-5.png" alt="Unravelling The Mind" class="podcast-cover">
                <div class="meta">
                    <div class="podcast-title">Unravelling The Mind</div>
                    <div class="podcast-category">Healthy Lifestyle</div>
                    <div class="podcast-duration">10 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-cover-6.png" alt="Talk Show - Ep8" class="podcast-cover">
                <div class="meta">
                    <div class="podcast-title">Talk Show - Ep8</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>
        </div>

        <!-- Gradient Overlay -->
        <div class="gradient-overlay"></div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <img src="images/headphones-icon.png" alt="Headphones" class="nav-icon">
            <img src="images/compass-icon.png" alt="Discover" class="nav-icon">
            <img src="images/heart-icon.png" alt="Favorites" class="nav-icon active">
            <img src="images/profile-icon.png" alt="Profile" class="nav-icon">
        </div>
    </div>
</body>
</html>
